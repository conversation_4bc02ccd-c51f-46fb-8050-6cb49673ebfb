<div class="bg-[#fff] shadow-[0px_3px_30px_#00000029] rounded-[20px] ml-6 mr-6 p-6">
  <div class="flex justify-between items-center mb-4">
    <div>
      <button class="text-[#0C8040]" [matMenuTriggerFor]="menu" *ngIf="permissionService.hasPermission('Gestion commerciale', 'Offre Standard', 'C')">
        <span class="relative top-[5px]"><mat-icon>cloud_upload</mat-icon></span>
        Importer
      </button>
    </div>
    <div class="min-w-[300px] bg-white">
      <app-select-version [data]="{ rubric: 'transport', nature: 'Standard' }"></app-select-version>
    </div>
  </div>
  <div class="relative">
    <!-- Single component with dropdown selector -->
    <app-grille-palettes></app-grille-palettes>
  </div>
</div>
<mat-menu #menu="matMenu" >
  <div class="p-3" (click)="$event.stopPropagation()">
    <label class="block mb-2 font-medium text[#0C8040" for="excel_file">Importer un fichier</label>
    <input
      class="block w-full text-sm text-black bg-white rounded-[28px] border-[3px] border-[#DBDBDB] cursor-pointer focus:outline-none dark:placeholder-gray-400 p-4"
      id="excel_file" type="file"
      accept=".xls,.xlsx, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
      (change)="uploadFile($event)" />
    <!-- add check boxe for update mode -->
    <div class="grid grid-cols-2 mt-5">
      <div><button class="underline">Guide</button></div>
      <div class="text-right">
        <button *ngIf="permissionService.hasPermission('Gestion commerciale', 'Offre Standard', 'C')" (click)="onSubmitForm()" mat-button class="bg-btn-green text-[18px] p-3">
          <span class="text-white ">
            Importer
          </span>
        </button>
      </div>
    </div>
  </div>
</mat-menu>
