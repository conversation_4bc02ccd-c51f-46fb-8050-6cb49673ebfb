import { HorsnomsDialogComponent } from './../grille-horsnorms/horsnoms-dialog/horsnoms-dialog.component';
import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { AppState } from 'app/core/store/app.states';
import {
  fetchActivateGrid,
  importGridDetails,
} from 'app/core/store/grids/grids.actions';
import {
  selectActiveGrid,
  selectActiveGridGroupByNature,
  selectActiveGridServices,
  selectGrid,
  selectHorsnormTransport,
} from 'app/core/store/grids/grids.selectors';
import { ToastService } from 'app/services';
import { MatDialog } from '@angular/material/dialog';
import { PermissionService } from 'app/core/services/permission.service';

@Component({
  selector: 'app-tarifaire-tab-groupe',
  templateUrl: './tarifaire-tab-groupe.component.html',
  styleUrls: ['./tarifaire-tab-groupe.component.css'],
})
export class TarifaireTabGroupeComponent implements OnInit {
  selectedTabIndex = 0;
  file = null;
  activeGrid: any;
  gridsByNature = {};
  gridsServices = [];
  grid_hornorme = [];
  isloaded: boolean = false;
  selectedImportType: string = '';
  public get nature_products(): string[] {
    return Object.keys(this.gridsByNature);
  }

  constructor(
    private store: Store<AppState>,
    private _toast: ToastService,
    public dialog: MatDialog,
    public permissionService: PermissionService
  ) {}

  ngOnInit(): void {
    this.store.select(selectGrid).subscribe((results) => {
      if (results) {
        this.activeGrid = results;
      } else {
        this.store
          .select(selectActiveGrid)
          .subscribe((res) => (this.activeGrid = res?.grid_active));
      }
    });

    // Dispatch initial fetch with no pagination to get the grid structure
    this.store.dispatch(fetchActivateGrid({ params: null }));

    this.store
      .select(selectActiveGridGroupByNature)
      .subscribe((res) => (this.gridsByNature = res));
    this.store
      .select(selectActiveGridServices)
      .subscribe((res) => (this.gridsServices = res));
    this.store.select(selectHorsnormTransport).subscribe((res) => {
      this.grid_hornorme = res;
      this.grid_hornorme.length > 0
        ? (this.isloaded = true)
        : (this.isloaded = false);
    });
  }

  // No longer need tab change handling as we're using a dropdown selector

  uploadFile(event: Event) {
    this.file = (event.target as HTMLInputElement).files[0];
  }

  onSubmitForm() {
    if (!this.activeGrid?.id) {
      this._toast.warn("Verifier qu'une grille est actif !");
      return;
    }
    if (!this.file) {
      this._toast.warn('Pas de fichier sélectioné !');
      return;
    }

    // Get the selected import type from the dropdown
    const importType = this.getSelectedImportType();
    if (!importType) {
      this._toast.warn('Veuillez sélectionner un type de données à importer !');
      return;
    }

    let formData: any = new FormData();
    formData.append('file', this.file);
    formData.append('grid_id', this.activeGrid?.id);
    formData.append('update_mode', 0);

    // Set rubric and nature based on import type
    if (importType === 'colis' || importType === 'palette') {
      formData.append('rubric', 'transport');
      formData.append('nature', importType);
    } else if (importType === 'services') {
      formData.append('rubric', 'services');
    } else if (importType === 'horsnorme') {
      formData.append('rubric', 'horsnorme');
    }

    this.store.dispatch(importGridDetails({ data: formData }));
  }

  private getSelectedImportType(): string | null {
    return this.selectedImportType || null;
  }
  openHorsnorms(): void {
    this.dialog.open(HorsnomsDialogComponent, {
      disableClose: true,
      width: '831px',
      data: {},
    });
  }
}

// Todo: clear file input when submited , add pagination.
