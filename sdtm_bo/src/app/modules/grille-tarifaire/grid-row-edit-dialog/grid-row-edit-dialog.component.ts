import { Component, OnInit, Inject } from '@angular/core';
import { <PERSON><PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Store } from '@ngrx/store';
import { AppState } from 'app/core/store/app.states';
import { ToastService } from 'app/services';
import { BoGridService } from 'app/core/services/admin-bo/bo-grids.service';
import { selectAllCity } from 'app/core/store/resources/resources.selectors';
import { fetchActivateGrid } from 'app/core/store/grids/grids.actions';

@Component({
  selector: 'app-grid-row-edit-dialog',
  templateUrl: './grid-row-edit-dialog.component.html',
  styleUrls: ['./grid-row-edit-dialog.component.css']
})
export class GridRowEditDialogComponent implements OnInit {
  editForm: FormGroup;
  isLoading = false;
  cities: any[] = [];
  calculBasis: any[] = [];
  rubrics: any[] = [];
  rowType: string; // 'transport' or 'services'

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<GridRowEditDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private store: Store<AppState>,
    private toastService: ToastService,
    private gridService: BoGridService
  ) {
    this.rowType = this.data.type || 'transport';
  }

  ngOnInit(): void {
    this.initializeForm();
    this.loadCities();
    this.loadCalculBasis();
    if (this.rowType === 'services') {
      this.loadRubrics();
    }
  }

  initializeForm(): void {
    const rowData = this.data.rowData || {};
    
    this.editForm = this.fb.group({
      originable_id: [rowData.originable_id || null],
      destinationable_id: [rowData.destinationable_id || null],
      calcul_basis_id: [rowData.calcul_basis_id || null, Validators.required],
      min: [rowData.min || 0],
      max: [rowData.max || 999999],
      calcul_val: [rowData.calcul_val || 0, [Validators.required, Validators.min(0)]],
      max_val: [rowData.max_val || 0],
      min_val: [rowData.min_val || 0],
      min_by_package: [rowData.min_by_package || 0],
      rubric_id: [rowData.rubric_id || null],
      type_val: [rowData.type_val || 'PRICE'],
      u_sup: [rowData.u_sup || 0],
      val_sup: [rowData.val_sup || 0]
    });

    // Add conditional validators based on row type
    if (this.rowType === 'services') {
      this.editForm.get('rubric_id')?.setValidators([Validators.required]);
    }
  }

  loadCities(): void {
    this.store.select(selectAllCity).subscribe(cities => {
      if (cities && cities.length > 0) {
        this.cities = cities;
      }
    });
  }

  loadCalculBasis(): void {
    // This should be loaded from a service or store
    // For now, using mock data - replace with actual service call
    this.calculBasis = [
      { id: 1, title_affichage: 'Poids' },
      { id: 2, title_affichage: 'Volume' },
      { id: 3, title_affichage: 'Forfait' }
    ];
  }

  loadRubrics(): void {
    // This should be loaded from a service or store
    // For now, using mock data - replace with actual service call
    this.rubrics = [
      { id: 2, title_affichage: 'Livraison' },
      { id: 3, title_affichage: 'Emballage' },
      { id: 4, title_affichage: 'Assurance' }
    ];
  }

  onSubmit(): void {
    if (this.editForm.invalid) {
      this.toastService.warn('Veuillez corriger les erreurs dans le formulaire.');
      return;
    }

    this.isLoading = true;
    const formData = this.editForm.value;
    const uuid = this.data.rowData?.uuid;

    if (uuid) {
      // Update existing row
      this.updateGridRow(uuid, formData);
    } else {
      // Create new row (if needed)
      this.createGridRow(formData);
    }
  }

  private updateGridRow(uuid: string, data: any): void {
    this.gridService.updateGeneralGridDetail(data, uuid).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.toastService.success('Ligne tarifaire mise à jour avec succès');
        this.dialogRef.close(true);
        // Refresh the grid data
        this.store.dispatch(fetchActivateGrid({ params: null }));
      },
      error: (error) => {
        this.isLoading = false;
        this.toastService.error('Erreur lors de la mise à jour de la ligne tarifaire');
        console.error('Update error:', error);
      }
    });
  }

  private createGridRow(data: any): void {
    this.gridService.createGeneralGridDetail(data).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.toastService.success('Ligne tarifaire créée avec succès');
        this.dialogRef.close(true);
        // Refresh the grid data
        this.store.dispatch(fetchActivateGrid({ params: null }));
      },
      error: (error) => {
        this.isLoading = false;
        this.toastService.error('Erreur lors de la création de la ligne tarifaire');
        console.error('Create error:', error);
      }
    });
  }
}
