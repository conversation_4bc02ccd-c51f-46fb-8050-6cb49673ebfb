import { Component, OnInit, Inject } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Store } from '@ngrx/store';
import { AppState } from 'app/core/store/app.states';
import { ToastService } from 'app/services';
import { BoGridService } from 'app/core/services/admin-bo/bo-grids.service';
import { selectAllCity } from 'app/core/store/resources/resources.selectors';
import { fetchActivateGrid } from 'app/core/store/grids/grids.actions';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-grid-row-edit-dialog',
  templateUrl: './grid-row-edit-dialog.component.html',
  styleUrls: ['./grid-row-edit-dialog.component.css']
})
export class GridRowEditDialogComponent implements OnInit {
  editForm: FormGroup;
  isLoading = false;
  cities: any[] = [];
  calculBasis: any[] = [];
  rubrics: any[] = [];
  rowType: string; // 'transport' or 'services'
  isDataLoading = true;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<GridRowEditDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private store: Store<AppState>,
    private toastService: ToastService,
    private gridService: BoGridService
  ) {
    this.rowType = this.data.type || 'transport';
  }

  ngOnInit(): void {
    this.initializeForm();
    this.loadAllRequiredData();
  }

  initializeForm(): void {
    const rowData = this.data.rowData || {};

    this.editForm = this.fb.group({
      // Location fields
      originable_id: [rowData.originable_id || null],
      destinationable_id: [rowData.destinationable_id || null],
      originable_type: [rowData.originable_type || 'CITY'],
      destinationable_type: [rowData.destinationable_type || 'CITY'],

      // Calculation fields
      calcul_basis_id: [rowData.calcul_basis_id || null, Validators.required],
      sup_calcul_basis_id: [rowData.sup_calcul_basis_id || null],

      // Range fields
      min: [rowData.min || 0],
      max: [rowData.max || 999999],

      // Value fields
      calcul_val: [rowData.calcul_val || 0, [Validators.required, Validators.min(0)]],
      max_val: [rowData.max_val || 0],
      min_val: [rowData.min_val || 0],
      min_by_package: [rowData.min_by_package || 0],
      max_poids: [rowData.max_poids || 0],

      // Service specific fields
      rubric_id: [rowData.rubric_id || null],
      type_val: [rowData.type_val || 'PRICE'],
      u_sup: [rowData.u_sup || 0],
      val_sup: [rowData.val_sup || 0],

      // Product category
      type_product_category: [rowData.type_product_category || this.rowType],
      prod_category_id: [rowData.prod_category_id || null],

      // Grid association
      offerable_id: [rowData.offerable_id || null],
      offerable_type: [rowData.offerable_type || 'Grid']
    });

    // Add conditional validators based on row type
    if (this.rowType === 'services') {
      this.editForm.get('rubric_id')?.setValidators([Validators.required]);
    }
  }

  loadAllRequiredData(): void {
    this.isDataLoading = true;

    // Load cities from store
    this.store.select(selectAllCity).subscribe(cities => {
      if (cities && cities.length > 0) {
        this.cities = cities;
      }
    });

    // Load calculation basis from API
    this.gridService.getAllBasisCalcul().subscribe({
      next: (response: any) => {
        this.calculBasis = response.data || response || [];
        this.checkDataLoadingComplete();
      },
      error: (error) => {
        console.error('Error loading calculation basis:', error);
        this.checkDataLoadingComplete();
      }
    });

    // Load rubrics if needed for services
    if (this.rowType === 'services') {
      this.gridService.getRubrics().subscribe({
        next: (response: any) => {
          this.rubrics = response.data || response || [];
          this.checkDataLoadingComplete();
        },
        error: (error) => {
          console.error('Error loading rubrics:', error);
          this.checkDataLoadingComplete();
        }
      });
    } else {
      this.checkDataLoadingComplete();
    }
  }

  checkDataLoadingComplete(): void {
    // Check if all required data is loaded
    const citiesLoaded = this.cities.length > 0;
    const calculBasisLoaded = this.calculBasis.length > 0;
    const rubricsLoaded = this.rowType !== 'services' || this.rubrics.length > 0;

    if (citiesLoaded && calculBasisLoaded && rubricsLoaded) {
      this.isDataLoading = false;
    }
  }

  onSubmit(): void {
    if (this.editForm.invalid) {
      this.toastService.warn('Veuillez corriger les erreurs dans le formulaire.');
      return;
    }

    this.isLoading = true;
    const formData = { ...this.editForm.value };
    const uuid = this.data.rowData?.uuid;

    // Set required fields based on row type
    if (this.rowType === 'transport') {
      formData.rubric_id = 1; // Transport rubric ID
      formData.type_product_category = this.rowType;
    }

    // Ensure grid association is set
    if (!formData.offerable_id && this.data.gridId) {
      formData.offerable_id = this.data.gridId;
      formData.offerable_type = 'Grid';
    }

    // Clean up null/undefined values
    Object.keys(formData).forEach(key => {
      if (formData[key] === null || formData[key] === undefined || formData[key] === '') {
        delete formData[key];
      }
    });

    if (uuid) {
      // Update existing row
      this.updateGridRow(uuid, formData);
    } else {
      // Create new row (if needed)
      this.createGridRow(formData);
    }
  }

  private updateGridRow(uuid: string, data: any): void {
    this.gridService.updateGeneralGridDetail(data, uuid).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.toastService.success('Ligne tarifaire mise à jour avec succès');
        this.dialogRef.close(true);
        // Refresh the grid data
        this.store.dispatch(fetchActivateGrid({ params: null }));
      },
      error: (error) => {
        this.isLoading = false;
        this.toastService.error('Erreur lors de la mise à jour de la ligne tarifaire');
        console.error('Update error:', error);
      }
    });
  }

  private createGridRow(data: any): void {
    this.gridService.createGeneralGridDetail(data).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.toastService.success('Ligne tarifaire créée avec succès');
        this.dialogRef.close(true);
        // Refresh the grid data
        this.store.dispatch(fetchActivateGrid({ params: null }));
      },
      error: (error) => {
        this.isLoading = false;
        this.toastService.error('Erreur lors de la création de la ligne tarifaire');
        console.error('Create error:', error);
      }
    });
  }
}
