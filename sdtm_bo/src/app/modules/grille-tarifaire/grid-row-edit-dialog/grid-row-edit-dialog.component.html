<div class="dialog">
  <div class="grid grid-cols-3 gap-4 bg-[#E9E9E9] rounded-[12px_12px_0px_0px] p-4">
    <h1 mat-dialog-title class="col-span-2">Modifier la ligne tarifaire</h1>
    <div class="text-right cursor-pointer" mat-button mat-dialog-close>
      <mat-icon>close</mat-icon>
    </div>
  </div>
  
  <form [formGroup]="editForm" (ngSubmit)="onSubmit()">
    <div mat-dialog-content class="p-6">
      
      <!-- Origin and Destination for transport types -->
      <div *ngIf="rowType === 'transport'" class="grid grid-cols-2 gap-4 mb-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Origine</label>
          <select formControlName="originable_id" 
                  class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0C8040]">
            <option value="">Toutes les villes</option>
            <option *ngFor="let city of cities" [value]="city.id">{{ city.name }}</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Destination</label>
          <select formControlName="destinationable_id" 
                  class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0C8040]">
            <option value="">Toutes les villes</option>
            <option *ngFor="let city of cities" [value]="city.id">{{ city.name }}</option>
          </select>
        </div>
      </div>

      <!-- Base de calcul -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">Base de calcul</label>
        <select formControlName="calcul_basis_id" 
                class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0C8040]">
          <option *ngFor="let basis of calculBasis" [value]="basis.id">{{ basis.title_affichage }}</option>
        </select>
      </div>

      <!-- Min/Max range for transport -->
      <div *ngIf="rowType === 'transport'" class="grid grid-cols-2 gap-4 mb-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Minimum</label>
          <input type="number" 
                 formControlName="min" 
                 class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0C8040]"
                 placeholder="0">
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Maximum</label>
          <input type="number" 
                 formControlName="max" 
                 class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0C8040]"
                 placeholder="999999">
        </div>
      </div>

      <!-- Values -->
      <div class="grid grid-cols-3 gap-4 mb-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Valeur de calcul</label>
          <input type="number" 
                 step="0.01"
                 formControlName="calcul_val" 
                 class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0C8040]"
                 placeholder="0.00">
        </div>
        
        <div *ngIf="rowType === 'transport'">
          <label class="block text-sm font-medium text-gray-700 mb-2">Maximum par expédition</label>
          <input type="number" 
                 step="0.01"
                 formControlName="max_val" 
                 class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0C8040]"
                 placeholder="0.00">
        </div>
        
        <div *ngIf="rowType === 'transport'">
          <label class="block text-sm font-medium text-gray-700 mb-2">Minimum par expédition</label>
          <input type="number" 
                 step="0.01"
                 formControlName="min_val" 
                 class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0C8040]"
                 placeholder="0.00">
        </div>
      </div>

      <!-- Transport specific fields -->
      <div *ngIf="rowType === 'transport'" class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">Minimum par colis</label>
        <input type="number" 
               step="0.01"
               formControlName="min_by_package" 
               class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0C8040]"
               placeholder="0.00">
      </div>

      <!-- Services specific fields -->
      <div *ngIf="rowType === 'services'" class="grid grid-cols-2 gap-4 mb-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Rubrique</label>
          <select formControlName="rubric_id" 
                  class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0C8040]">
            <option *ngFor="let rubric of rubrics" [value]="rubric.id">{{ rubric.title_affichage }}</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Type de valeur</label>
          <select formControlName="type_val" 
                  class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0C8040]">
            <option value="PRICE">Prix (DH)</option>
            <option value="COEF">Coefficient (%)</option>
          </select>
        </div>
      </div>

      <!-- Services additional fields -->
      <div *ngIf="rowType === 'services'" class="grid grid-cols-2 gap-4 mb-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Unité supplémentaire</label>
          <input type="number" 
                 formControlName="u_sup" 
                 class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0C8040]"
                 placeholder="0">
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Valeur supplémentaire</label>
          <input type="number" 
                 step="0.01"
                 formControlName="val_sup" 
                 class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0C8040]"
                 placeholder="0.00">
        </div>
      </div>

      <!-- Validation errors -->
      <div *ngIf="editForm.invalid && editForm.touched" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
        <p class="text-red-600 text-sm">Veuillez corriger les erreurs dans le formulaire.</p>
      </div>
    </div>

    <div class="grid grid-cols-2 gap-4 mt-5 mb-5 p-4">
      <div>
        <button type="button" 
                class="underline w-32 pt-2 mr-3 font-bold text-[18px]" 
                mat-dialog-close>
          Annuler
        </button>
      </div>
      <div class="text-right">
        <button type="submit" 
                [disabled]="editForm.invalid || isLoading"
                mat-button 
                class="bg-btn-green text-[18px] p-3 disabled:opacity-50">
          <span class="text-white">
            {{ isLoading ? 'Enregistrement...' : 'Enregistrer' }}
          </span>
        </button>
      </div>
    </div>
  </form>
</div>
