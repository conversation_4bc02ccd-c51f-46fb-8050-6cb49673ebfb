import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'app/shared';
import { TarifaireTabGroupeComponent } from './tarifaire-tab-groupe/tarifaire-tab-groupe.component';
import { GrillePalettesComponent } from './grille-palettes/grille-palettes.component';
import { GrilleHorsnormsComponent } from './grille-horsnorms/grille-horsnorms.component';
import { GrilleServiceComponent } from './grille-service/grille-service.component';
import { SelectVersionComponent } from './select-version/select-version.component';
import { ConfirmationDialogComponent } from './grille-palettes/confirmation-dialog/confirmation-dialog.component';
import { HorsnomsDialogComponent } from './grille-horsnorms/horsnoms-dialog/horsnoms-dialog.component';
import { AddVersionComponent } from './add-version/add-version.component';
import { GridRowEditDialogComponent } from './grid-row-edit-dialog/grid-row-edit-dialog.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { PaginatorModule } from 'primeng-lts/paginator';

@NgModule({
  declarations: [
    TarifaireTabGroupeComponent,
    GrillePalettesComponent,
    GrilleHorsnormsComponent,
    GrilleServiceComponent,
    SelectVersionComponent,
    ConfirmationDialogComponent,
    HorsnomsDialogComponent,
    AddVersionComponent,
    GridRowEditDialogComponent,
  ],
  imports: [
    CommonModule,
    SharedModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    FormsModule,
    PaginatorModule
  ],
})
export class GrilleTarifaireModule { }
