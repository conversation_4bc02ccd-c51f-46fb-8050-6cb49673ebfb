<div class="grid mt-4">
  <!-- Data type selector -->
  <div class="flex items-center mb-4">
    <label class="mr-2 font-medium">Type de données:</label>
    <select
      [(ngModel)]="selectedType"
      (change)="onDataTypeChange()"
      class="p-2 border rounded-md">
      <option *ngFor="let type of dataTypes" [value]="type.value">{{ type.label }}</option>
    </select>
  </div>

  <!-- Filters -->
  <app-shared-filter
    [inputs]="inputsFilter"
    (filter)="filtrer($event)"
  ></app-shared-filter>
</div>

<div *ngIf="isLoading" class="flex justify-center items-center h-64">
  <mat-spinner></mat-spinner>
</div>

<ng-template #noData>
  <div class="flex justify-center items-center h-64">
    <p>Aucune donnée disponible</p>
  </div>
</ng-template>

<div *ngIf="!isLoading">
  <div class="overflow-x-auto" *ngIf="gridData && gridData.length > 0; else noData">
    <table class="w-full text-left text-gray-500 mt-4">
      <thead>
        <tr>
          <th *ngFor="let header of headerColumuns" scope="col" class="p-3 text-[#0C8040] font-normal whitespace-nowrap">
            <div class="flex items-center">
              {{ header }}
            </div>
          </th>
          <th scope="col" class="p-3 text-[#0C8040] font-normal whitespace-nowrap">Actions</th>
        </tr>
      </thead>
      <tbody>
        <!-- Palette and Colis data -->
        <ng-container *ngIf="selectedType === 'palette' || selectedType === 'colis'">
          <tr class="table-row bg-white whitespace-nowrap border-b border-gray-800"
              *ngFor="let grid of gridData">
            <td class="p-3">{{ grid.originable?.name || 'Toutes' }}</td>
            <td class="p-3">{{ grid.destinationable?.name || 'Toutes' }}</td>
            <td class="p-3">{{ grid.basis_calcul?.title_affichage }}</td>
            <td class="p-3">{{ grid.min }} - {{ grid.max }}</td>
            <td class="p-3">{{ grid.calcul_val }}</td>
            <td class="p-3">{{ grid.max_val }}</td>
            <td class="p-3">{{ grid.min_val }}</td>
            <td class="p-3">{{ grid.min_by_package }}</td>
            <td class="p-3">
              <button (click)="editRow(grid)"
                      class="text-[#0C8040] hover:text-[#065f30] mr-2"
                      title="Modifier">
                <mat-icon>edit</mat-icon>
              </button>
            </td>
          </tr>
        </ng-container>

        <!-- Services data -->
        <ng-container *ngIf="selectedType === 'services'">
          <tr class="table-row bg-white whitespace-nowrap border-b border-gray-800"
              *ngFor="let grid of gridData">
            <td class="p-3">{{ grid.originable?.name || 'Tout' }}</td>
            <td class="p-3">{{ grid.destinationable?.name || 'Tout' }}</td>
            <td class="p-3">{{ grid.rubric?.title_affichage }}</td>
            <td class="p-3">{{ grid.basis_calcul?.title_affichage }}</td>
            <td class="p-3">
              {{
                grid.type_val === "COEF"
                ? convertToPourcentage(grid.calcul_val)
                : grid.calcul_val
              }}
              {{ grid.type_val === "PRICE" ? " DH" : "" }}
              {{ grid.type_val === "COEF" ? " %" : "" }}
            </td>
            <td class="p-3">{{ grid.min || '-' }}</td>
            <td class="p-3">{{ grid.max || '-' }}</td>
            <td class="p-3">{{ grid.u_sup || '-' }}</td>
            <td class="p-3">{{ grid.val_sup || '-' }}</td>
            <td class="p-3">
              <button (click)="editRow(grid)"
                      class="text-[#0C8040] hover:text-[#065f30] mr-2"
                      title="Modifier">
                <mat-icon>edit</mat-icon>
              </button>
            </td>
          </tr>
        </ng-container>

        <!-- Hors normes data -->
        <ng-container *ngIf="selectedType === 'horsnorme'">
          <tr class="table-row bg-white whitespace-nowrap border-b border-gray-800"
              *ngFor="let grid of gridData">
            <td class="p-3">{{ grid.categorie_produit?.title }}</td>
            <td class="p-3">{{ grid.calcul_val }}</td>
            <td class="p-3">
              <button (click)="editRow(grid)"
                      class="text-[#0C8040] hover:text-[#065f30] mr-2"
                      title="Modifier">
                <mat-icon>edit</mat-icon>
              </button>
            </td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>

  <div class="flex justify-center mt-4">
    <p-paginator
      [rows]="itemsPerPage"
      [totalRecords]="totalItems"
      [first]="(page-1) * itemsPerPage"
      (onPageChange)="paginate($event)"
      [rowsPerPageOptions]="[10, 15, 20, 25, 30]">
    </p-paginator>
  </div>
</div>
