import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Store } from '@ngrx/store';
import { AppState } from 'app/core/store/app.states';
import { selectAllCity } from 'app/core/store/resources/resources.selectors';
import { selectActiveGrid, selectGridIsLoading } from 'app/core/store/grids/grids.selectors';
import { fetchActivateGrid } from 'app/core/store/grids/grids.actions';
import { Subscription } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { GridRowEditDialogComponent } from '../grid-row-edit-dialog/grid-row-edit-dialog.component';

@Component({
  selector: 'app-grille-palettes',
  templateUrl: './grille-palettes.component.html',
  styleUrls: ['./grille-palettes.component.css'],
})
export class GrillePalettesComponent implements OnInit, OnDestroy {
  // Data type options
  dataTypes = [
    { value: 'palette', label: 'Palette' },
    { value: 'colis', label: 'Colis' },
    { value: 'horsnorme', label: 'Hors Nor<PERSON>' },
    { value: 'services', label: 'Services' }
  ];

  // Selected data type
  selectedType: string = 'palette';

  // Pagination state for each data type
  paginationState: { [key: string]: { page: number, itemsPerPage: number } } = {
    'palette': { page: 1, itemsPerPage: 15 },
    'colis': { page: 1, itemsPerPage: 15 },
    'horsnorme': { page: 1, itemsPerPage: 15 },
    'services': { page: 1, itemsPerPage: 15 }
  };

  // Headers for different types
  transportHeaders = [
    'Origine',
    'Destination',
    'Base de calcul',
    'Tranche',
    'Valeur',
    'Maximum par expédition',
    'Minimum par expédition',
    'Minimum par colis'
  ];

  servicesHeaders = [
    'Origine',
    'Destination',
    'Rubrique',
    'Base de calcul',
    'Valeur',
    'Min',
    'Max',
    'Unité supplémentaire',
    'Valeur supplémentaire'
  ];

  horsnormeHeaders = [
    'Label',
    'Valeur'
  ];

  // Current headers based on selected type
  headerColumuns = this.transportHeaders;

  // Pagination state
  page: number = 1;
  totalItems: number = 0;
  itemsPerPage: number = 15;
  isLoading: boolean = false;
  gridData: any[] = [];

  // Subscriptions to clean up
  private subscriptions: Subscription[] = [];

  // Filter values
  filters: any = {
    villeoriginid: null,
    villedestinationid: null
  };

  inputsFilter = [
    {
      name: 'villeoriginid',
      placeholder: 'Origine',
      type: 'select',
      options: []
    },
    {
      name: 'villedestinationid',
      placeholder: 'Destination',
      type: 'select',
      options: []
    }
  ];

  constructor(
    private store: Store<AppState>,
    private dialog: MatDialog
  ) { }

  ngOnInit(): void {
    // Load cities for filter dropdowns
    const citySub = this.store.select(selectAllCity).subscribe((cities) => {
      if (cities && cities.length > 0) {
        this.inputsFilter[0].options = [];
        this.inputsFilter[1].options = [];

        for (let i = 0; i < cities.length; i++) {
          this.inputsFilter[0].options.push({
            'text': cities[i].name,
            'value': cities[i].id,
          });

          this.inputsFilter[1].options.push({
            'text': cities[i].name,
            'value': cities[i].id,
          });
        }
      }
    });
    this.subscriptions.push(citySub);

    // Subscribe to loading state
    const loadingSub = this.store.select(selectGridIsLoading).subscribe(loading => {
      this.isLoading = loading;
    });
    this.subscriptions.push(loadingSub);

    // Subscribe to grid data
    const gridSub = this.store.select(selectActiveGrid).subscribe(activeGrid => {
      if (activeGrid) {

        console.log('Active grid:', activeGrid);

        // For Palette and Colis (both are transport with different type_product_category)
        if ((this.selectedType === 'palette' || this.selectedType === 'colis') &&
            activeGrid.grids_details &&
            activeGrid.grids_details.transport) {

          const transportData = activeGrid.grids_details.transport as any;
          if (transportData && typeof transportData === 'object') {
            this.gridData = transportData.data || [];
            this.totalItems = transportData.total || 0;
          }
        }
        // For Services
        else if (this.selectedType === 'services' &&
                activeGrid.grids_details &&
                activeGrid.grids_details.services) {

          const servicesData = activeGrid.grids_details.services as any;
          if (servicesData && typeof servicesData === 'object') {
            this.gridData = servicesData.data || [];
            this.totalItems = servicesData.total || 0;
          }
        }
        // For Hors Normes
        else if (this.selectedType === 'horsnorme' &&
                activeGrid.grids_details_hors_norme &&
                activeGrid.grids_details_hors_norme.transport) {

          const horsnormeData = activeGrid.grids_details_hors_norme.transport as any;
          if (horsnormeData && typeof horsnormeData === 'object') {
            this.gridData = horsnormeData.data || [];
            this.totalItems = horsnormeData.total || 0;
          }
        }
      }
    });
    this.subscriptions.push(gridSub);

    // Initialize with first page of data
    this.loadData();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadData() {
    // Determine the nature and type_product_category based on the selected type
    let nature = 'transport';
    let type_product_category = null;

    if (this.selectedType === 'palette' || this.selectedType === 'colis') {
      nature = 'transport';
      type_product_category = this.selectedType;
    } else if (this.selectedType === 'horsnorme') {
      nature = 'horsnorme';
    } else if (this.selectedType === 'services') {
      nature = 'services';
    }

    // Get the current pagination state for the selected type
    const currentState = this.paginationState[this.selectedType];
    this.page = currentState.page;
    this.itemsPerPage = currentState.itemsPerPage;

    // Create params object with all required parameters
    const params: any = {
      nature: nature,
      page: this.page,
      per_page: this.itemsPerPage
    };

    // Add type_product_category if it's not null
    if (type_product_category) {
      params.type_product_category = type_product_category;
    }

    // Add filters if they exist
    if (this.filters.villeoriginid) {
      params.villeOriginId = this.filters.villeoriginid;
    }

    if (this.filters.villedestinationid) {
      params.villeDestinationId = this.filters.villedestinationid;
    }

    // Dispatch action to fetch grid data from store
    this.store.dispatch(fetchActivateGrid({ params }));
  }

  // Handle data type change
  onDataTypeChange() {
    // Update headers based on selected type
    if (this.selectedType === 'palette' || this.selectedType === 'colis') {
      this.headerColumuns = this.transportHeaders;
    } else if (this.selectedType === 'services') {
      this.headerColumuns = this.servicesHeaders;
    } else if (this.selectedType === 'horsnorme') {
      this.headerColumuns = this.horsnormeHeaders;
    }

    // Load data with the current pagination state for the selected type
    this.loadData();
  }

  paginate($event: any) {
    this.itemsPerPage = $event.rows;
    this.page = $event.page + 1;

    // Save the current pagination state
    this.paginationState[this.selectedType] = {
      page: this.page,
      itemsPerPage: this.itemsPerPage
    };

    // Load data with the updated pagination
    this.loadData();
  }

  filtrer($event: any) {
    // Store the filter values (convert to lowercase for consistency)
    this.filters = $event;

    console.log('Filter values:', this.filters);

    // Reset to first page when filtering
    this.page = 1;

    // Update the pagination state for the current type
    this.paginationState[this.selectedType] = {
      page: 1,
      itemsPerPage: this.itemsPerPage
    };

    // Load data with the new filters
    this.loadData();
  }

  refreshFilter() {
    // Reset filters to empty values
    this.filters = {
      villeoriginid: null,
      villedestinationid: null
    };

    // Reset to first page when refreshing filters
    this.page = 1;

    // Update the pagination state for the current type
    this.paginationState[this.selectedType] = {
      page: 1,
      itemsPerPage: this.itemsPerPage
    };

    // Load data with reset filters
    this.loadData();
  }

  // Helper method to convert coefficient to percentage
  convertToPourcentage(coef: number): number {
    if (!coef) return 0;
    coef = coef * 100;
    return Math.round((coef + Number.EPSILON) * 100) / 100;
  }

  // Method to open edit dialog for a grid row
  editRow(rowData: any): void {
    const dialogRef = this.dialog.open(GridRowEditDialogComponent, {
      width: '800px',
      disableClose: true,
      data: {
        rowData: rowData,
        type: this.selectedType
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Refresh the data after successful edit
        this.loadData();
      }
    });
  }
}
