<?php

namespace App\Modules\Expedition\Services;

use Illuminate\Support\Facades\Cache;
use App\Modules\Rubric\Repositories\RubricRepository;
use App\Modules\Grid\Repositories\GridRepository;
use App\Modules\Offer\Repositories\OfferRepository;
use App\Modules\Customer\Repositories\CustomerRepository;
use App\Enums\eGridType;
use App\Enums\eOfferStatus;

class TarificationCacheService
{
    protected $rubricRepository;
    protected $gridRepository;
    protected $offerRepository;
    protected $customerRepository;

    public function __construct(
        RubricRepository $rubricRepository,
        GridRepository $gridRepository,
        OfferRepository $offerRepository,
        CustomerRepository $customerRepository
    ) {
        $this->rubricRepository = $rubricRepository;
        $this->gridRepository = $gridRepository;
        $this->offerRepository = $offerRepository;
        $this->customerRepository = $customerRepository;
    }

    /**
     * Get cached rubrics
     */
    public function getCachedRubrics()
    {
        return Cache::remember('tarification_rubrics', 3600, function() {
            return $this->rubricRepository->getAll();
        });
    }

    /**
     * Get cached public grid details with location filtering
     */
    public function getCachedPublicGridDetails($originCityId, $destCityId)
    {
        $cacheKey = "public_grid_details_{$originCityId}_{$destCityId}";

        return Cache::remember($cacheKey, 1800, function() use ($originCityId, $destCityId) {
            $default_grid = $this->gridRepository
                ->with(['details' => function($query) use ($originCityId, $destCityId) {
                    $query->where(function($q) use ($destCityId) {
                        $q->whereNull('destinationable_id')
                          ->orWhere('destinationable_id', $destCityId);
                    })
                    ->where(function($q) use ($originCityId) {
                        $q->whereNull('originable_id')
                          ->orWhere('originable_id', $originCityId);
                    })
                    ->with(['destinationable', 'basis_calcul', 'sup_basis_calcul']);
                }])
                ->findFirst([['is_activated', 1], ['type', eGridType::_PUBLIC]]);

            return $default_grid ? $default_grid->details : collect();
        });
    }

    /**
     * Get cached customer grid details with location filtering
     */
    public function getCachedCustomerGridDetails($customerId, $originCityId, $destCityId)
    {
        $cacheKey = "customer_grid_details_{$customerId}_{$originCityId}_{$destCityId}";

        return Cache::remember($cacheKey, 900, function() use ($customerId, $originCityId, $destCityId) {
            $client = $this->customerRepository
                ->with(['grid' => function($query) use ($originCityId, $destCityId) {
                    $query->with(['details' => function($detailQuery) use ($originCityId, $destCityId) {
                        $detailQuery->where(function($q) use ($destCityId) {
                            $q->whereNull('destinationable_id')
                              ->orWhere('destinationable_id', $destCityId);
                        })
                        ->where(function($q) use ($originCityId) {
                            $q->whereNull('originable_id')
                              ->orWhere('originable_id', $originCityId);
                        })
                        ->with(['destinationable', 'basis_calcul', 'sup_basis_calcul']);
                    }]);
                }])
                ->find($customerId);

            return [
                'client' => $client,
                'grid_details' => $client && $client->grid ? $client->grid->details : collect()
            ];
        });
    }

    /**
     * Get cached customer offers with location filtering
     */
    public function getCachedCustomerOffers($customerId, $originCityId, $destCityId)
    {
        $cacheKey = "customer_offers_{$customerId}_{$originCityId}_{$destCityId}";

        return Cache::remember($cacheKey, 900, function() use ($customerId, $originCityId, $destCityId) {
            $offers = $this->offerRepository
                ->with(['details' => function($query) use ($originCityId, $destCityId) {
                    $query->where(function($q) use ($destCityId) {
                        $q->whereNull('destinationable_id')
                          ->orWhere('destinationable_id', $destCityId);
                    })
                    ->where(function($q) use ($originCityId) {
                        $q->whereNull('originable_id')
                          ->orWhere('originable_id', $originCityId);
                    })
                    ->with(['destinationable', 'basis_calcul', 'sup_basis_calcul']);
                }])
                ->findMany([['id_customer', $customerId], ['status', eOfferStatus::_CREATED]]);

            return $offers->pluck('details')->flatten();
        });
    }

    /**
     * Clear cache for specific customer
     */
    public function clearCustomerCache($customerId)
    {
        $patterns = [
            "customer_grid_details_{$customerId}_*",
            "customer_offers_{$customerId}_*"
        ];
        
        foreach ($patterns as $pattern) {
            $this->clearCacheByPattern($pattern);
        }
    }

    /**
     * Clear cache by pattern (simplified implementation)
     */
    protected function clearCacheByPattern($pattern)
    {
        // In a real implementation, you might want to use Redis SCAN
        // or maintain a list of cache keys for more efficient clearing
        Cache::flush(); // For now, we'll use a simple flush
    }

    /**
     * Clear all tarification cache
     */
    public function clearAllCache()
    {
        Cache::forget('tarification_rubrics');
        Cache::flush(); // Clear all cache for simplicity
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats()
    {
        return [
            'rubrics_cached' => Cache::has('tarification_rubrics'),
            'cache_driver' => config('cache.default')
        ];
    }
}
