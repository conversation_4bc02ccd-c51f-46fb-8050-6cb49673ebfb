<?php

namespace App\Modules\Grid\Http\Controllers;

use App\Enums\eGridType;
use App\Enums\eRespCode;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use App\Modules\Grid\Models\Grid;
use App\Enums\eExpeditionItemType;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Modules\Grid\Services\GridService;
use App\Modules\Grid\Models\GridAffretement;
use App\Modules\Grid\Models\TypeAffretement;
use App\Modules\Grid\Http\Requests\GridRequest;
use Illuminate\Support\Facades\DB;

class GridController extends Controller
{
    use ApiResponser;

    protected $gridService;

    public function __construct(GridService $gridService)
    {
        $this->gridService = $gridService;
    }

    public function index(Request $request) {
        $result = $this->gridService->all($request);
        return $result->toJson();
    }

    /**
     * Display the module welcome screen
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getActivatedGrid(Request $request)
    {
        $result = $this->gridService->getActivatedGrid($request);
        return $result->toJson();
    }

    public function getService()
    {

        $result = $this->gridService->getService();
        return $result->toJson();
    }

    public function getServiceByPage(Request $request)
    {
        $result = $this->gridService->getService($request['page']);
        return $result->toJson();
    }

    // public function getGridDetailsHorsnorme()
    // {
    //     $result = $this->gridService->getGridDetailsHorsnorme();
    //     return $result->toJson();
    // }


    public function import(GridRequest $request)
    {
        $result = $this->gridService->import($request);
        return $result->toJson();
    }

    public function storeGridHorsnorm(Request $request)
    {
        $result = $this->gridService->createGridHorsnorm($request->all());
        return $result->toJson();
    }

    public function updateGridHorsnorm(Request $request, $uuid)
    {
        $result = $this->gridService->updateGridHornorm($request->all(), $uuid);
        return $result->toJson();
    }

    public function destroyGridHorsnorm($uuid)
    {
        $result = $this->gridService->deleteGridHornorm($uuid);
        return $result->toJson();
    }

    public function updateGridDetail(Request $request, $uuid)
    {
        $result = $this->gridService->updateGridDetail($request->all(), $uuid);
        return $result->toJson();
    }

    public function createGridDetail(Request $request)
    {
        $result = $this->gridService->createGridDetail($request->all());
        return $result->toJson();
    }

     /**
     * Display the specified resource.
     *
     * @param  \App\Models\Offer  $offer
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Grid $grid)
    {
        $result = $this->gridService->get($request, $grid);
        return $result->toJson();
    }

    /**
    * Update the specified resource in storage.
    *
    * @param  \Illuminate\Http\Request  $request
    * @param  int  $id
    * @return \Illuminate\Http\Response
    */
    public function update(GridRequest $request,  Grid $grid)
    {
        $result = $this->gridService->update($request->validated(), $grid);
        return $result->toJson();
    }


    public function getAffretmentGrids(){
        $result = $this->gridService->getAffretmentGrids();
        return $result->toJson();
    }

    public function getAffretmentDetailsTransport(Request $request , $uuid){


        $grid = GridAffretement::firstWhere('uuid' , '=', $uuid);

        $result = $this->gridService->getAffretmentDetailsTransport($request, $grid);
        return $result->toJson();
    }



    public function getAffretmentDetailsServiesByGrid(Request $request , $uuid){
        $grid = GridAffretement::firstWhere('uuid' , '=', $uuid);
        Log::info('grid'.json_encode($grid));
        $result = $this->gridService->getAffretmentDetailsServiesByGrid($request, $grid);
        return $result->toJson();
    }



    public function getAffretmentDetailsServiesGlobal(Request $request){

        $grid = GridAffretement::where('is_activated' , true)->where("tonnage_id", null)->where('truck_type_id', null)->get()->first();

        Log::info('grid before ' . json_encode($grid));

        $result = $this->gridService->getAffretmentDetailsServiesGlobal($request, $grid);

        return $result->toJson();
    }


    public function getAffretmentTypes(Request $request){
        return response()->json(TypeAffretement::all(), 200,);
    }



    public function importGridAffretment(Request $request ){
        $result = $this->gridService->importGridAffretment($request->all());
        return $result->toJson();
    }


    public function getTransportConditionsAffretment(Request $request){
        $result = $this->gridService->getTransportConditionsAffretment($request);
        return $result->toJson();
    }


    public function getServicesConditionsAffretment(Request $request){
        $result = $this->gridService->getServicesConditionsAffretment($request);
        return $result->toJson();
    }


    public function getGlobalServicesConditions(Request $request){
        $result = $this->gridService->getGlobalServicesConditions($request);
        return $result->toJson();
    }

    public function getAffretementGridsTransport(){
        $result = GridAffretement::where('type', 'TRANSPORT')
        ->groupBy('title', 'baseCalcul', 'is_activated')
        ->select('title', 'baseCalcul', 'is_activated', DB::raw('MAX(created_at) as created_at'))
        ->get()->toArray();
        return $result;
    }
    public function getAffretementGridsSpecific(){
        $result = GridAffretement::where('type', 'SERVICE_BY_TRUCK')
        ->groupBy('title', 'is_activated')
        ->select('title', 'is_activated', DB::raw('MAX(created_at) as created_at'))
        ->get()->toArray();
        return $result;
    }
    public function getAffretementGridsGlobal(){
        $result = GridAffretement::where('type', 'SERVICE_GLOBAL')
        ->groupBy('title', 'is_activated')
        ->select('title', 'is_activated', DB::raw('MAX(created_at) as created_at'))
        ->get()->toArray();
        return $result;
    }
    public function affretementActiveGrid(Request $request){
        GridAffretement::where(['type' => $request->type, 'is_activated' => true])->update(['is_activated' => false]);
        GridAffretement::where(['type' => $request->type, 'is_activated' => false, 'title' => $request->title, 'baseCalcul' => $request->baseCalcul])->update(['is_activated' => true]);
        return ['message' => 'SUCCESSFULLY UPDATED'];
    }
    public function affretementImportOffre(Request $request){

        $result = $this->gridService->importOffreAffretement($request);
        return $result->toJson();

    }






}
