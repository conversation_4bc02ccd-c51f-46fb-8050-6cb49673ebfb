<?php
namespace App\Modules\Grid\Services;

use Log;
use Exception;
use App\Enums\eGridType;
use App\Enums\eRespCode;
use App\Enums\eTaxeValType;
use App\Traits\ApiResponser;
use App\Modules\Grid\Models\Grid;
use App\Enums\eExpeditionItemType;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Validator;
use App\Modules\Grid\Models\GridAffretement;
use App\Modules\Grid\Repositories\GridRepository;
use App\Modules\Grid\Http\Imports\GridPublicImport;
use App\Modules\Grid\Models\GridAffretementDetails;
use App\Modules\ProductCategory\Models\ProductCategory;
use App\Modules\Grid\Http\Imports\OffreAffretementImport;
use App\Modules\OfferGridDetails\Models\OfferGridDetails;
use App\Modules\Grid\Http\Imports\GridPublicAffretmentImport;
use App\Modules\OfferGridDetails\Repositories\OfferGridDetailsRepository;

class GridService
{
    use ApiResponser;

    public function __construct(OfferGridDetailsRepository $offerGridDetailsRepository, GridRepository $gridRepository)
    {
        $this->offerGridDetailsRepository = $offerGridDetailsRepository;
        $this->gridRepository = $gridRepository;
    }

    public function all($request)
    {
        $type = $request->type ?? null; $result = Grid::get();
        if($type) {
            $result = Grid::where('type', $type)->get();
            foreach($result as $key => $grid) {
                $grid_transport = [];
                $grid_services =  [];
                $grid_horsnorme_tr = [];
                $result[$key]['grids_details'] = ['services' => $grid_services , 'transport' => $grid_transport];
                $result[$key]['grids_details_hors_norme'] = ['transport' => $grid_horsnorme_tr];
            }
            return $this->mainResponse(eRespCode::_200_GRD_LIST_OK, true, $result);
        }
        return $this->mainResponse(eRespCode::_200_GRD_LIST_OK, true, $result);
    }

    public function getGridDetailsTransport($grid_active, $page = null, $perPage = 15, $filters = [])
    {
        $nature_products = [eExpeditionItemType::_COLIS , eExpeditionItemType::_PALETTE];
        $query = $grid_active->details()
                            ->where('rubric_id','=', 1) // Transport
                            ->whereIn('type_product_category',$nature_products)
                            ->with(['originable','destinationable', 'basis_calcul']);

        // Apply filters
        if (!empty($filters)) {
            if (isset($filters['villeOriginId']) && !empty($filters['villeOriginId'])) {
                $query->where('originable_id', $filters['villeOriginId']);
            }

            if (isset($filters['villeDestinationId']) && !empty($filters['villeDestinationId'])) {
                
                $query->where('destinationable_id', $filters['villeDestinationId']);
            }
        }

        if ($page !== null) {
            $grid_transport = $query->paginate($perPage);
        } else {
            $grid_transport = $query->get();
        }

        return $grid_transport;
    }

    public function getGridDetailsHorsnorme($grid_active)
    {

        $nature_products = eExpeditionItemType::_HORS_NORME;
        $product_category = ProductCategory::where('type', $nature_products)->get()->pluck('id')->toArray();
        $offer_grid_details =  $grid_active->details()
                                           ->where('rubric_id','=', 1)
                                           ->whereIn('prod_category_id',$product_category)
                                           ->with(['originable','categorie_produit','destinationable', 'basis_calcul'])
                                           ->get();
        return $offer_grid_details;
    }

    public function getGridDetailsServices($grid_active, $page = null, $perPage = 15, $filters = [])
    {
        $query = $grid_active->details()
            ->where('rubric_id','<>', 1)
            ->with(['originable','destinationable', 'basis_calcul' , 'rubric']);

        // Apply filters
        if (!empty($filters)) {
            if (isset($filters['villeOriginId']) && !empty($filters['villeOriginId'])) {
                $query->whereHas('originable', function($q) use ($filters) {
                    $q->where('id', $filters['villeOriginId']);
                });
            }

            if (isset($filters['villeDestinationId']) && !empty($filters['villeDestinationId'])) {
                $query->whereHas('destinationable', function($q) use ($filters) {
                    $q->where('id', $filters['villeDestinationId']);
                });
            }
        }

        if ($page !== null) {
            $grid_services = $query->paginate($perPage);
        } else {
            $grid_services = $query->get();
        }

        return $grid_services;
    }

    public function getActivatedGrid($request = null)
    {
        $grid_active = Grid::where('is_activated', true)->firstWhere('type',eGridType::_PUBLIC);
        if(!isset($grid_active)) return $this->mainResponse(eRespCode::_400_BAD_REQUEST, false , ['message' => "Grids not found !"]);

        $page = $request ? $request->get('page', null) : null;
        $perPage = $request ? $request->get('per_page', 15) : 15;
        $nature = $request ? $request->get('nature', null) : null;

        // Extract filters from request
        $filters = [];
        if ($request) {
            if ($request->has('villeOriginId')) {
                $filters['villeOriginId'] = $request->get('villeOriginId');
            }

            if ($request->has('villeDestinationId')) {
                $filters['villeDestinationId'] = $request->get('villeDestinationId');
            }
        }


        if ($nature) {
            if ($nature === 'transport') {
                $grid_transport = $this->getGridDetailsTransport($grid_active, $page, $perPage, $filters);
                $result = [
                    'grid_active' => $grid_active,
                    'grids_details' => ['transport' => $grid_transport]
                ];
            } else if ($nature === 'services') {
                $grid_services = $this->getGridDetailsServices($grid_active, $page, $perPage, $filters);
                $result = [
                    'grid_active' => $grid_active,
                    'grids_details' => ['services' => $grid_services]
                ];
            } else if ($nature === 'horsnorme') {
                $grid_horsnorme_tr = $this->getGridDetailsHorsnorme($grid_active);
                $result = [
                    'grid_active' => $grid_active,
                    'grids_details_hors_norme' => ['transport' => $grid_horsnorme_tr]
                ];
            }
        } 
        else {
            $grid_transport = [];
            $grid_services = [];
            $grid_horsnorme_tr = [];
            $result = [
                'grid_active' => $grid_active,
                'grids_details' => ['services' => $grid_services , 'transport' => $grid_transport],
                'grids_details_hors_norme' => ['transport' => $grid_horsnorme_tr]
            ];
        }

        return $this->mainResponse(eRespCode::_200_GRD_ACTIVE_GET_OK, false , $result);
    }

    public function getService()
    {
        $grid_active = Grid::where('is_activated', true)->firstWhere('type',eGridType::_PUBLIC);
        $grid_services = $this->getGridDetailsServices($grid_active);
        $value = $grid_services->total() / $grid_services->perPage();
        $page = (int)ceil($value);
        // $grid_services->total($page);
        // dd($total);
        // $grid_services = OfferGridDetails::where('rubric_id','<>',1)->paginate(10);
        return $this->mainResponse(eRespCode::_200_GRD_ACTIVE_GET_OK,false,$grid_services);
    }

    public function import($request)
    {
        // Creating new public grid
        DB::beginTransaction();
        $grid_title = $request['grid_title'] ?? null;
        if($grid_title){
            $grid = $this->gridRepository->create(['title' => $grid_title ,'type' => eGridType::_PUBLIC]);
            if(!$grid){ DB::rollback(); return $this->mainResponse(eRespCode::_500_INTERNAL_ERROR, false); }
            $request['grid_id'] = $grid->id;
        }


        // deleting transport grids details for specific nature
        if($request['update_mode'] && !isset($grid_title) && $request['rubric'] == "transport")
        {
            OfferGridDetails::where('offerable_type', 'Grid')
                            ->where('offerable_id', $request['grid_id'])
                            ->where('type_product_category', $request['nature'])
                            ->where('rubric_id', 1)
                            ->delete();

        }
        // deleting services grids details
        if($request['update_mode'] && $request['rubric'] != "transport"){

            OfferGridDetails::where('offerable_type', 'Grid')
                            ->where('offerable_id', $request['grid_id'])
                            ->where('rubric_id','<>', 1)
                            ->delete();

        }

        // Import
        try{
            Excel::import(new GridPublicImport($this->offerGridDetailsRepository, $request), $request['file']);
        }catch(Exception $e) {
            DB::rollback();
            Log::error('Grid public import error : '.$e->getMessage());
            return $this->mainResponse(eRespCode::_400_BAD_REQUEST, false , ['message' => $e->getMessage()]);
        }
        DB::commit();

        // Get imported data
        $result = null;
        $grid_active = Grid::where('is_activated', true)->firstWhere('type', eGridType::_PUBLIC);
        if(isset($grid_active)){
            $result = ['rubric' => $request['rubric']];
            $result['data'] = $request['rubric'] == "transport" ?
                $this->getGridDetailsTransport($grid_active) :
                $this->getGridDetailsServices($grid_active);
        }
        return $this->mainResponse(eRespCode::_200_GRD_IMPORT_OK, true , $result);

    }


    public function get($request, $grid)
    {
        $grid->load(['details.basis_calcul', 'details.max_basis_calcul', 'details.categorie_produit', 'details.sup_basis_calcul', 'details' => function($q) use ($request) {
            $q->wherehas('rubric', function($qr) use ($request) {
                $qr->when($request->has('isTransport') && $request['isTransport'], function($query) {
                    return $query->where('title', 'TRANSPORT');
                })
                ->when($request->has('isTransport') && !$request['isTransport'], function($query) {
                    return $query->where('title', '<>','TRANSPORT');
                });
            });
        }]);

        if ($request->has('isTransport') && $request['isTransport']) {
            return $this->mainResponse(eRespCode::_200_U_GET_OK, true, ['uuid' => $grid->uuid , 'id' => $grid->id , 'title' => $grid->title] );
        }
        else{

            // services
            $details_ids = $grid->details->pluck('rubric_id')->toArray();

            //call getActivatedGrid
            $grid_active = Grid::where('is_activated', true)->firstWhere('type', eGridType::_PUBLIC);

            $grid_transport = $this->getGridDetailsTransport($grid_active);
            $grid_services = $this->getGridDetailsServices($grid_active);

            $grid->details->load(['rubric' , 'originable' , 'destinationable' , 'basis_calcul' , 'categorie_produit']);

            $active_details_ids = [];
            if ($request->has('isTransport') && !$request['isTransport']) {
                $active_details_ids = $grid_services->pluck('rubric_id')->toArray();
            }

            // compare details_ids and active_details_ids
            $diff = array_diff($active_details_ids, $details_ids);
            $missed_services = [];


            if(count($diff) > 0){
                // getd diff ids from active grid
                $missed_services = $grid_active->details()
                    ->whereIn('rubric_id', $diff)
                    ->with(['originable','destinationable', 'basis_calcul' , 'rubric'])
                    ->get();

                Log::info('missed services ='.json_encode($diff));
                $grid->details = $grid->details->merge($missed_services);
            }

            $grid->details = $grid->details->groupBy(['originable_type' , 'originable_id','rubric_id' , 'destinationable_type' , 'destinationable_id' , 'calcul_basis_id' , 'type_product_category' , 'prod_category_id'])->flatten(7);

            $grid->details = $grid->details->map(function($item, $key) {
                $item = [
                    'id' => $item->first()->rubric_id,
                    'title' => $item->first()->rubric->title,
                    'details' => [
                        'originable_type' => $item->first()->originable_type,
                        'origine' => $item->first()->originable,
                        'destinationable_type' => $item->first()->destinationable_type,
                        'destination' => $item->first()->destinationable,
                        'basis_calcul' => $item->first()->basis_calcul,
                        'product_category' => $item->first()->categorie_produit ?? null,
                        'rubric' => $item->first()->rubric,
                        'type_product_category' => $item->first()->type_product_category,
                        'tranches' => $item->sortBy([
                            ['min', 'asc'],
                            ['max', 'desc'],
                        ])
                    ]
                ];
                return $item;
            });

            $grid->details = $grid->details->groupBy('id');


            $grid->details = $grid->details->map(function($item, $key) {
                $item = [
                    'id' => $item->first()['id'],
                    'title' => $item->first()['title'],
                    'details' => $item->map(function($i , $k){
                        return $i['details'];
                    })
                ];
                return $item;
            });


            $grid->details = $grid->details->values();

            return $this->mainResponse(eRespCode::_200_U_GET_OK, true, [
                'grid' => [
                    'id' => $grid->id,
                    'uuid' => $grid->uuid,
                    'title' => $grid->title,
                    'type' => $grid->type,
                    'services' => $grid->details,
                ]
            ]);

     }
    }

    public function createGridHorsnorm($request)
    {
        try {
            $validator = Validator::make($request, [
                'prod_category_id' => 'required|integer',
                'offerable_id' => 'required|integer',
                'price' => 'required|integer',
            ]);
            if ($validator->fails()) {
                return $this->mainResponse(eRespCode::_400_BAD_REQUEST, false, $validator->errors());
            }
            $payload = $validator->validated();
            $data = [
                'prod_category_id' => $payload['prod_category_id'],
                'offerable_id' => $payload['offerable_id'],
                'calcul_val' => $payload['price'],
                'offerable_type'=> "Grid",
                'type_product_category'=> eExpeditionItemType::_HORS_NORME,
                'calcul_basis_id'=> 1,
                'type_product_category' => eExpeditionItemType::_HORS_NORME,
                'min'=> 1,
                'max'=> 1,
                'rubric_id'=> 1,
                'type_val'=>eTaxeValType::_PRICE
            ];
            $offerGridDetail = $this->offerGridDetailsRepository->create($data);
            $offerGridDetail = $offerGridDetail->with(['originable','categorie_produit','destinationable', 'basis_calcul'])->latest()->first();
            return $this->mainResponse(eRespCode::_200_A_CREATE_OK, true, $offerGridDetail);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            $message = $e->getMessage();
            return $this->mainResponse(eRespCode::_500_INTERNAL_ERROR, false, $message);
        }
    }

    public function updateGridHornorm($request, $uuid)
    {
        $validator = Validator::make($request, [
            'prod_category_id' => 'required|integer',
            'offerable_id' => 'required|integer',
            'price' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->mainResponse(eRespCode::_400_BAD_REQUEST, false, $validator->errors());
        }
        $payload = $validator->validated();
        $data = [
            'prod_category_id' => $payload['prod_category_id'],
            'offerable_id' => $payload['offerable_id'],
            'calcul_val' => $payload['price'],
            'offerable_type'=> "Grid",
            'type_product_category'=> eExpeditionItemType::_HORS_NORME,
            'calcul_basis_id'=> 1,
            'type_product_category' => eExpeditionItemType::_HORS_NORME,
            'min'=>1,
            'max'=>1,
            'rubric_id'=>1,
            'type_val'=>eTaxeValType::_PRICE
        ];
        $pos    = $this->offerGridDetailsRepository->findOrFail($uuid , 'uuid');
        $result = $this->offerGridDetailsRepository->update($pos,$data)->with(['originable','categorie_produit','destinationable', 'basis_calcul'])->where('uuid',$uuid)->get()->first();

        if (!$result) return $this->mainResponse(eRespCode::_500_INTERNAL_ERROR, false);
            return $this->mainResponse(eRespCode::_200_A_UPDATE_OK, true, $result);


    }





    public function update($request, $grid)
    {
        DB::beginTransaction();
        $grid = $this->gridRepository->update($grid, $request);
        $grid_active = Grid::where('is_activated', true)->where('id', '<>', $grid->id)->firstWhere('type', eGridType::_PUBLIC);

        if ($request['is_activated'] ?? null) {
            if ($grid_active) $grid_active = $this->gridRepository->update($grid_active, ['is_activated' => false]);
        }
         // get details
        $grid_transport = $this->getGridDetailsTransport($grid);
        $grid_services = $this->getGridDetailsServices($grid);
        $grid_horsnorme_tr = $this->getGridDetailsHorsnorme($grid);

        $grid['grids_details'] = ['services' => $grid_services , 'transport' => $grid_transport];
        $grid['grids_details_hors_norme'] = ['transport' => $grid_horsnorme_tr];

        DB::commit();
        if (!$grid) return $this->mainResponse(eRespCode::_500_INTERNAL_ERROR, false);
        return $this->mainResponse(eRespCode::_200_GRD_UPDATE_OK, true, $grid);
    }


    public function deleteGridHornorm($uuid)
    {
        $pos = $this->offerGridDetailsRepository->findOrFail($uuid , 'uuid');
        $result = $this->offerGridDetailsRepository->delete($pos);
        if (!$result) return $this->mainResponse(eRespCode::_500_INTERNAL_ERROR, false);
        return $this->mainResponse(eRespCode::_200_A_DELETE_OK, true);
    }

    public function updateGridDetail($data, $uuid)
    {
        try {
            $gridDetail = $this->offerGridDetailsRepository->findOrFail($uuid, 'uuid');
            $result = $this->offerGridDetailsRepository->update($gridDetail, $data);

            if (!$result) {
                return $this->mainResponse(eRespCode::_500_INTERNAL_ERROR, false);
            }

            // Get the updated record with relationships
            $updatedDetail = $this->offerGridDetailsRepository
                ->with(['originable', 'destinationable', 'basis_calcul', 'rubric'])
                ->where('uuid', $uuid)
                ->first();

            return $this->mainResponse(eRespCode::_200_A_UPDATE_OK, true, $updatedDetail);
        } catch (Exception $e) {
            Log::error('Grid detail update error: ' . $e->getMessage());
            return $this->mainResponse(eRespCode::_500_INTERNAL_ERROR, false, ['message' => $e->getMessage()]);
        }
    }

    public function createGridDetail($data)
    {
        try {
            $result = $this->offerGridDetailsRepository->create($data);

            if (!$result) {
                return $this->mainResponse(eRespCode::_500_INTERNAL_ERROR, false);
            }

            // Get the created record with relationships
            $createdDetail = $this->offerGridDetailsRepository
                ->with(['originable', 'destinationable', 'basis_calcul', 'rubric'])
                ->where('id', $result->id)
                ->first();

            return $this->mainResponse(eRespCode::_200_A_CREATE_OK, true, $createdDetail);
        } catch (Exception $e) {
            Log::error('Grid detail create error: ' . $e->getMessage());
            return $this->mainResponse(eRespCode::_500_INTERNAL_ERROR, false, ['message' => $e->getMessage()]);
        }
    }


    public function getAffretmentGrids(){

        $affretmentGrids = GridAffretement::where('is_activated' , true)->where("tonnage_id",'!=' , null)->where('truck_type_id', '!=' , null)->get();

        return $this->mainResponse(eRespCode::_200_GRD_AFF_LIST_OK, true, $affretmentGrids);
    }



    public function getAffretmentDetailsTransport($request, $grid)
    {

        $grid->load(['details.basis_calcul', 'details' => function($q) use ($request) {
            $q->wherehas('rubric', function($qr) use ($request) {
                    return $qr->where('title', 'TRANSPORT');
            });
        }]);


        $grid->details = $grid->details->groupBy(['ville_origin_id' , 'ville_destination_id','rubric_id' , 'calcul_basis_affretement_id' ])->flatten(3);

        $grid->details = $grid->details->map(function($item, $key) {
            $item = [
                'id' => $item->first()->rubric_id,
                'title' => $item->first()->rubric->title,
                'details' => [
                    'ville_origin_id' => $item->first()->ville_origin_id,
                    'origine' => $item->first()->origine,
                    'ville_destination_id' => $item->first()->ville_destination_id,
                    'destination' => $item->first()->destination,
                    'basis_calcul' => $item->first()->basis_calcul,
                    'rubric' => $item->first()->rubric,
                    'tranche_prix' => $item->first()->tranche_prix
                ]
            ];
            return $item;
        });

        $grid->details = $grid->details->groupBy('id');


        $grid->details = $grid->details->map(function($item, $key) {
            $item = [
                'id' => $item->first()['id'],
                'title' => $item->first()['title'],
                'details' => $item->map(function($i , $k){
                    return $i['details'];
                })
            ];
            return $item;
        });


        $grid->details = $grid->details->values();

        return $this->mainResponse(eRespCode::_200_U_GET_OK, true, [
            'grid' => [
                'id' => $grid->id,
                'uuid' => $grid->uuid,
                'title' => $grid->title,
                'type' => $grid->type,
                'services' => $grid->details,
            ]
        ]);

    }


public function getAffretmentDetailsServiesGlobal($request, $gridService) {
    // Step 1: Log the input $gridService to check if it contains the correct data
    Log::info('Input Grid Service: ' . json_encode($gridService));

    $gridService->load(['details.basis_calcul', 'details' => function($q) use ($request) {
        $q->wherehas('rubric', function($qr) use ($request) {
            return $qr->where('title', '<>', 'TRANSPORT');
        });
    }]);

    // Step 2: Log the $gridService after loading the details to check if filtering is applied
    Log::info('Grid Service after loading details: ' . json_encode($gridService));

    $gridService->details = $gridService->details->groupBy(['ville_origin_id', 'ville_destination_id', 'rubric_id'])->flatten(2);

    $gridService->details = $gridService->details->map(function($item, $key) {
        $item = [
            'id' => $item->first()->rubric_id,
            'title' => $item->first()->rubric->title,
            'details' => [
                'ville_origin_id' => $item->first()->ville_origin_id,
                'origine' => $item->first()->origine,
                'ville_destination_id' => $item->first()->ville_destination_id,
                'destination' => $item->first()->destination,
                'basis_calcul' => $item->first()->basis_calcul,
                'rubric' => $item->first()->rubric,
                'tranches' => $item->sortBy([
                    ['min', 'asc'],
                    ['max', 'desc'],
                ])
            ]
        ];
        return $item;
    });

    $gridService->details = $gridService->details->groupBy('id');

    $gridService->details = $gridService->details->map(function($item, $key) {
        $item = [
            'id' => $item->first()['id'],
            'title' => $item->first()['title'],
            'details' => $item->map(function($i , $k){
                return $i['details'];
            })
        ];
        return $item;
    });

    $gridService->details = $gridService->details->values();

    Log::info('Final Grid Service: ' . json_encode($gridService));

    return $this->mainResponse(eRespCode::_200_U_GET_OK, true, [
        'grid' => [
            'id' => $gridService->id,
            'uuid' => $gridService->uuid,
            'title' => $gridService->title,
            'type' => $gridService->type,
            'services' => $gridService->details,
        ]
    ]);
}



    public function getAffretmentDetailsServiesByGrid($request, $gridService) {
    // Step 1: Log the input $gridService to check if it contains the correct data
    Log::info('Input Grid Service: ' . json_encode($gridService));

    $gridService->load(['details.basis_calcul', 'details' => function($q) use ($request) {
        $q->wherehas('rubric', function($qr) use ($request) {
            return $qr->where('title', '<>', 'TRANSPORT');
        });
    }]);

    // Step 2: Log the $gridService after loading the details to check if filtering is applied
    Log::info('Grid Service after loading details: ' . json_encode($gridService));

    $gridService->details = $gridService->details->groupBy(['ville_origin_id', 'ville_destination_id', 'rubric_id'])->flatten(2);

    $gridService->details = $gridService->details->map(function($item, $key) {
        $item = [
            'id' => $item->first()->rubric_id,
            'title' => $item->first()->rubric->title,
            'details' => [
                'ville_origin_id' => $item->first()->ville_origin_id,
                'origine' => $item->first()->origine,
                'ville_destination_id' => $item->first()->ville_destination_id,
                'destination' => $item->first()->destination,
                'basis_calcul' => $item->first()->basis_calcul,
                'rubric' => $item->first()->rubric,
                'tranches' => $item->sortBy([
                    ['min', 'asc'],
                    ['max', 'desc'],
                ])
            ]
        ];
        return $item;
    });

    $gridService->details = $gridService->details->groupBy('id');

    $gridService->details = $gridService->details->map(function($item, $key) {
        $item = [
            'id' => $item->first()['id'],
            'title' => $item->first()['title'],
            'details' => $item->map(function($i , $k){
                return $i['details'];
            })
        ];
        return $item;
    });

    $gridService->details = $gridService->details->values();

    Log::info('Final Grid Service: ' . json_encode($gridService));

    return $this->mainResponse(eRespCode::_200_U_GET_OK, true, [
        'grid' => [
            'id' => $gridService->id,
            'uuid' => $gridService->uuid,
            'title' => $gridService->title,
            'type' => $gridService->type,
            'services' => $gridService->details,
        ]
    ]);
    }


    public function importGridAffretment($request)
    {
        try{
            Excel::import(new GridPublicAffretmentImport($request), $request['file']);
            DB::commit();
        }catch(Exception $e) {
            DB::rollback();
            Log::error('Grid public Afrretment import error : '.$e->getMessage());
            return $this->mainResponse(eRespCode::_400_BAD_REQUEST, false , ['message' => $e->getMessage()]);
        }

        return $this->mainResponse(eRespCode::_200_GRD_IMPORT_OK, true);
    }


    public function getTransportConditionsAffretment($request)
    {
        $gridIds = GridAffretement::where(['is_activated' => true, 'type' => 'TRANSPORT'])->get()->pluck('id');



        $scopes = [
            'ByTonnage' => [$request->tonnage_id],
            'ByTruckType' => [$request->truck_type_id],
            'ByVilleOrigin' => [$request->villeoriginid],
            'ByVilleDestination' => [$request->villedestinationid],
            'ByPrixFixe' => [$request->prixfixe],
        ];



        $affretementDetails = GridAffretementDetails::whereIn('offerable_id', $gridIds)
            ->with(['offerable.tonnage:id,name', 'offerable.truck_type:id,name'])
            ->whereHas('rubric', function ($query) {
                $query->where('title', 'TRANSPORT');
            })
            ->where('offerable_type', 'App\Modules\Grid\Models\GridAffretement')
            ->scopes($scopes)
            ->get();

        return $this->mainResponse(eRespCode::_200_GRD_LIST_OK, true, $affretementDetails);
    }



    public function getServicesConditionsAffretment($request)
    {


        $scopes = [
            'ByTonnage' => [$request->tonnage_id],
            'ByTruckType' => [$request->truck_type_id],
            'BytranchePrix' => [$request->tranche_prix],
        ];



        // dd($request->prixfixe);

        $affretementDetails = GridAffretementDetails::
        with(['offerable.tonnage:id,name', 'offerable.truck_type:id,name'])->
        where('offerable_type' , 'App\Modules\Grid\Models\GridAffretement')->
        whereHas('offerable', function ($query) {
            $query->whereNotNull('truck_type_id')
                  ->whereNotNull('tonnage_id');
        })
        ->whereHas('rubric', function ($query) {
            $query->where('title', '!=', 'TRANSPORT');
        })
        ->scopes($scopes)
        ->get();

        return $this->mainResponse(eRespCode::_200_GRD_LIST_OK, true , $affretementDetails);
    }


    public function getGlobalServicesConditions($request)
    {

        $scopes = [
            'byRubrique' => [$request->rubrique],
            'BytranchePrix' => [$request->tranche_prix],
        ];
        $affretementDetails = GridAffretementDetails::
        where('offerable_type' , 'App\Modules\Grid\Models\GridAffretement')
        ->whereHas('offerable', function ($query) {
            $query->whereNull('truck_type_id')
                  ->whereNull('tonnage_id');
        })
        ->scopes($scopes)
        ->get();

        return $this->mainResponse(eRespCode::_200_GRD_LIST_OK, true , $affretementDetails);
    }

    public function importOffreAffretement($request)
    {
        try{
            Excel::import(new OffreAffretementImport($request), $request['file']);
            DB::commit();
        }catch(Exception $e) {
            DB::rollback();
            Log::error('Grid public Afrretment import error : '.$e->getMessage());
            return $this->mainResponse(eRespCode::_400_BAD_REQUEST, false , ['message' => $e->getMessage()]);
        }

        return $this->mainResponse(eRespCode::_200_GRD_IMPORT_OK, true);
    }


}


