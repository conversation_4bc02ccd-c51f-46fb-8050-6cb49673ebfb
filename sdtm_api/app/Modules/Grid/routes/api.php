<?php

use App\Modules\Grid\Http\Controllers\GridController;
use Illuminate\Support\Facades\Route;


Route::group(['prefix' => 'api'], function () {
    Route::apiResource('grids', 'GridController');
    Route::get('grids-active/',[ GridController::class, 'getActivatedGrid']);
    Route::get('grids-service',[GridController::class,'getService']);
    Route::get('grids-service-page',[GridController::class,'getServiceByPage']);
    Route::post('grids-import/',[ GridController::class, 'import']);
    Route::get('horsnomme',[ GridController::class, 'getGridDetailsHorsnorme']);
    Route::post('horsnomme',[ GridController::class, 'storeGridHorsnorm']);
    Route::put('updategridhorsnomme/{uuid}', [ GridController::class, 'updateGridHorsnorm']);
    Route::delete('deletegridhorsnomme/{uuid}',[GridController::class, 'destroyGridHorsnorm']);
    Route::put('update-grid-detail/{uuid}', [ GridController::class, 'updateGridDetail']);
    Route::post('create-grid-detail', [ GridController::class, 'createGridDetail']);


    // affretment grids list 
    Route::get('affretment-grids' , [GridController::class, 'getAffretmentGrids'] );

    Route::get('affretment-grid-details/{uuid}' , [GridController::class, 'getAffretmentDetailsTransport'] );



    // Route::get('affretment-public-services' , [GridController::class, 'getAffretmentDetailsServiesGlobal']);

    Route::get('affretment-services-by-grid/{uuid}' , [GridController::class, 'getAffretmentDetailsServiesByGrid']);

    Route::get('types-affretment' , [GridController::class, 'getAffretmentTypes']);
    
    Route::post('grids-import-affretment',[ GridController::class, 'importGridAffretment']);

    Route::get('grid-affretment-transport',[ GridController::class, 'getTransportConditionsAffretment']);

    Route::get('affretment-specefique-services',[ GridController::class, 'getServicesConditionsAffretment']);

    Route::get('affretment-public-services', [ GridController::class, 'getGlobalServicesConditions']);
    Route::get('affretment-get-grids-transport', [ GridController::class, 'getAffretementGridsTransport']);
    Route::get('affretment-get-grids-specific', [ GridController::class, 'getAffretementGridsSpecific']);
    Route::get('affretment-get-grids-global', [ GridController::class, 'getAffretementGridsGlobal']);
    Route::post('affretment-active-grid', [ GridController::class, 'affretementActiveGrid']);
    Route::post('affretement-import-offre', [ GridController::class, 'affretementImportOffre']);




});